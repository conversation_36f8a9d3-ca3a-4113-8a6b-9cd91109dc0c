<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建测试Excel文件</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <h1>创建测试Excel文件</h1>
    <button onclick="createTestFile()">创建包含日期的测试文件</button>
    
    <script>
        function createTestFile() {
            // 创建测试数据 - 模拟真实的运维日志数据
            const testData = [
                ['科陆流水线日常运维及故障处理情况'],
                ['日期', '故障处理情况', '空仓？', '记录人'],
                [new Date('2024-01-15'), '设备检修完成', '是', '张三'],
                [new Date('2024-01-15'), '系统重启', '是', '李四'],
                [new Date('2024-01-16'), '故障排除', '否', '王五'],
                [new Date('2024-01-16'), '日常维护', '否', '赵六'],
                [new Date('2024-01-17'), '设备清洁', '是', '钱七']
            ];

            // 创建工作簿
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(testData);
            
            // 设置日期列的格式
            const range = XLSX.utils.decode_range(ws['!ref']);
            for (let R = 2; R <= range.e.r; R++) {
                const cellAddress = XLSX.utils.encode_cell({ r: R, c: 0 });
                if (ws[cellAddress]) {
                    // 设置为日期类型
                    ws[cellAddress].t = 'd';
                    ws[cellAddress].s = { numFmt: 'yyyy-mm-dd' };
                }
            }

            // 合并主标题行
            ws['!merges'] = [{
                s: { r: 0, c: 0 },
                e: { r: 0, c: 3 }
            }];

            XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
            XLSX.writeFile(wb, '测试运维日志.xlsx');
            
            alert('测试文件已创建：测试运维日志.xlsx\n包含相同日期的多条记录，用于测试日期分组和合并功能。');
        }
    </script>
</body>
</html>
