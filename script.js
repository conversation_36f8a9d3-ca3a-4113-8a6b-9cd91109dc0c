// 全局变量
let selectedFile = null;
let workbookData = null;

// DOM元素引用
const fileInput = document.getElementById('fileInput');
const uploadArea = document.getElementById('uploadArea');
const fileInfo = document.getElementById('fileInfo');
const convertBtn = document.getElementById('convertBtn');
const progressSection = document.getElementById('progressSection');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const messageArea = document.getElementById('messageArea');

// 初始化事件监听器
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
});

function initializeEventListeners() {
    // 文件选择事件
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽事件
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);
    
    // 转换按钮事件
    convertBtn.addEventListener('click', handleConvert);
}

// 处理文件选择
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        validateAndProcessFile(file);
    }
}

// 处理拖拽悬停
function handleDragOver(event) {
    event.preventDefault();
    uploadArea.classList.add('dragover');
}

// 处理拖拽离开
function handleDragLeave(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
}

// 处理文件拖拽放置
function handleFileDrop(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        validateAndProcessFile(files[0]);
    }
}

// 验证和处理文件
function validateAndProcessFile(file) {
    // 验证文件类型
    if (!file.name.toLowerCase().endsWith('.xlsx')) {
        showMessage('请选择 .xlsx 格式的Excel文件', 'error');
        return;
    }
    
    // 验证文件大小 (限制为10MB)
    if (file.size > 10 * 1024 * 1024) {
        showMessage('文件大小不能超过10MB', 'error');
        return;
    }
    
    selectedFile = file;
    displayFileInfo(file);
    convertBtn.disabled = false;
    showMessage('文件选择成功，可以开始转换', 'success');
}

// 显示文件信息
function displayFileInfo(file) {
    document.getElementById('fileName').textContent = file.name;
    document.getElementById('fileSize').textContent = formatFileSize(file.size);
    document.getElementById('fileDate').textContent = new Date(file.lastModified).toLocaleString('zh-CN');
    
    fileInfo.style.display = 'block';
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 处理转换
async function handleConvert() {
    if (!selectedFile) {
        showMessage('请先选择文件', 'error');
        return;
    }
    
    try {
        // 禁用转换按钮
        convertBtn.disabled = true;
        convertBtn.textContent = '转换中...';
        
        // 显示进度
        showProgress(true);
        updateProgress(10, '正在读取文件...');
        
        // 读取文件
        const workbook = await readExcelFileWithExcelJS(selectedFile);
        updateProgress(50, '正在处理数据...');

        // 处理数据
        const processedData = processWorkbookDataFromExcelJS(workbook);
        updateProgress(70, '正在生成新文件...');

        // 生成新的Excel文件
        const newWorkbook = await createFormattedWorkbookWithExcelJS(processedData);
        updateProgress(90, '正在准备下载...');

        // 下载文件
        await downloadWorkbookWithExcelJS(newWorkbook);
        updateProgress(100, '转换完成！');
        
        showMessage('文件转换成功！已自动下载', 'success');
        
    } catch (error) {
        console.error('转换过程中发生错误:', error);
        showMessage('转换失败: ' + error.message, 'error');
    } finally {
        // 重置按钮状态
        setTimeout(() => {
            convertBtn.disabled = false;
            convertBtn.textContent = '开始转换';
            showProgress(false);
        }, 2000);
    }
}

// 读取文件为ArrayBuffer
function readFileAsArrayBuffer(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(new Error('文件读取失败'));
        reader.readAsArrayBuffer(file);
    });
}

// 使用ExcelJS读取Excel文件
async function readExcelFileWithExcelJS(file) {
    const arrayBuffer = await readFileAsArrayBuffer(file);
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(arrayBuffer);
    return workbook;
}

// 使用ExcelJS处理工作簿数据
function processWorkbookDataFromExcelJS(workbook) {
    // 获取第一个工作表
    const worksheet = workbook.worksheets[0];
    if (!worksheet) {
        throw new Error('工作簿中没有找到工作表');
    }

    // 提取数据
    const data = [];
    worksheet.eachRow((row, rowNumber) => {
        const rowData = [];
        row.eachCell((cell, colNumber) => {
            // 处理日期类型
            if (cell.type === ExcelJS.ValueType.Date) {
                rowData[colNumber - 1] = cell.value;
            } else {
                rowData[colNumber - 1] = cell.value;
            }
        });
        data.push(rowData);
    });

    if (data.length === 0) {
        throw new Error('工作表中没有数据');
    }

    // 获取标题行
    const headers = data[0].map(header => String(header || '').trim()).filter(h => h);

    if (headers.length === 0) {
        throw new Error('未找到有效的列标题');
    }

    console.log('检测到的列标题:', headers);

    // 过滤掉'记录人'列
    const filteredHeaders = [];
    const columnIndexMap = new Map();

    headers.forEach((header, index) => {
        if (!header.includes('记录人')) {
            columnIndexMap.set(filteredHeaders.length, index);
            filteredHeaders.push(header);
        }
    });

    // 添加新列
    filteredHeaders.push('备注', '维护保养情况');

    console.log('处理后的列标题:', filteredHeaders);

    // 处理数据行
    const dataRows = data.slice(1).filter(row =>
        row.some(cell => cell !== null && cell !== undefined && String(cell).trim() !== '')
    );

    console.log('有效数据行数:', dataRows.length);

    if (dataRows.length === 0) {
        throw new Error('没有找到有效的数据行');
    }

    // 找到日期列的索引
    const dateColumnIndex = findDateColumnIndex(filteredHeaders);
    console.log('日期列索引:', dateColumnIndex);

    // 转换数据行
    const processedRows = dataRows.map(row => {
        const newRow = [];

        // 复制原有列数据（除了记录人列）
        columnIndexMap.forEach((colIndex, newColIndex) => {
            let cellValue = row[colIndex] || '';

            // 如果是日期列，保持日期类型
            if (newColIndex === dateColumnIndex && cellValue) {
                cellValue = formatDateValue(cellValue);
            }

            newRow.push(cellValue);
        });

        // 添加新列数据
        newRow.push('已解决'); // 备注列
        newRow.push(''); // 维护保养情况列

        return newRow;
    });

    // 按日期分组数据
    const groupedData = groupDataByDate(processedRows, dateColumnIndex, filteredHeaders);

    console.log('分组后的数据行数:', groupedData.rows.length);
    console.log('合并信息:', groupedData.mergeInfo);

    return {
        headers: filteredHeaders,
        rows: groupedData.rows,
        mergeInfo: groupedData.mergeInfo
    };
}

// 处理工作簿数据
function processWorkbookData(workbook) {
    // 获取第一个工作表
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    // 将工作表转换为JSON数据
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    if (jsonData.length === 0) {
        throw new Error('Excel文件为空或格式不正确');
    }

    // 智能检测表头位置
    let headers = null;
    let dataStartRow = 1;

    // 检查第一行是否是合并的标题行（只有一列且包含"科陆流水线"）
    if (jsonData[0] && jsonData[0].length === 1 &&
        String(jsonData[0][0]).includes('科陆流水线')) {
        // 第一行是标题行，第二行是列标题
        headers = jsonData[1];
        dataStartRow = 2;
    } else {
        // 第一行就是列标题
        headers = jsonData[0];
        dataStartRow = 1;
    }

    if (!headers || headers.length === 0) {
        throw new Error('无法读取表头信息');
    }

    // 过滤掉'记录人'列并建立列映射
    const filteredHeaders = [];
    const columnIndexMap = [];

    headers.forEach((header, index) => {
        if (header !== '记录人') {
            filteredHeaders.push(header);
            columnIndexMap.push(index);
        }
    });

    // 添加新列
    filteredHeaders.push('备注', '维护保养情况');

    // 找到日期列的索引
    const dateColumnIndex = findDateColumnIndex(filteredHeaders);
    if (dateColumnIndex === -1) {
        throw new Error('未找到日期列，请确保Excel文件包含日期相关的列');
    }

    // 处理数据行
    const rawRows = [];
    for (let i = dataStartRow; i < jsonData.length; i++) {
        const row = jsonData[i];
        if (row && row.length > 0 && row.some(cell => cell !== undefined && cell !== '')) {
            const newRow = [];

            // 复制过滤后的列数据
            columnIndexMap.forEach((colIndex, newColIndex) => {
                let cellValue = row[colIndex] || '';

                // 如果是日期列，保持日期类型
                if (newColIndex === dateColumnIndex && cellValue) {
                    cellValue = formatDateValue(cellValue);
                }

                newRow.push(cellValue);
            });

            // 添加新列数据
            newRow.push('已解决'); // 备注列
            newRow.push(''); // 维护保养情况列

            rawRows.push(newRow);
        }
    }

    // 按日期分组并处理合并逻辑
    const groupedData = groupDataByDate(rawRows, dateColumnIndex, filteredHeaders);

    // 数据处理完成

    return {
        headers: filteredHeaders,
        rows: groupedData.rows,
        mergeInfo: groupedData.mergeInfo
    };
}

// 查找日期列的索引
function findDateColumnIndex(headers) {
    const dateKeywords = ['日期', 'date', '时间', 'time', '日', '月', '年'];

    for (let i = 0; i < headers.length; i++) {
        const header = String(headers[i]).toLowerCase();
        if (dateKeywords.some(keyword => header.includes(keyword))) {
            return i;
        }
    }

    return -1; // 未找到日期列
}

// 按日期分组数据并处理合并逻辑
function groupDataByDate(rows, dateColumnIndex, headers) {
    if (rows.length === 0) {
        return { rows: [], mergeInfo: [] };
    }

    // 按日期分组
    const dateGroups = new Map();

    rows.forEach((row, index) => {
        const dateGroupKey = getDateGroupKey(row[dateColumnIndex]);

        if (!dateGroups.has(dateGroupKey)) {
            dateGroups.set(dateGroupKey, []);
        }
        dateGroups.get(dateGroupKey).push({ row, originalIndex: index });
    });

    // 处理分组数据并应用合并逻辑
    const processedRows = [];
    const mergeInfo = [];

    // 找到关键列的索引
    const maintenanceColumnIndex = headers.indexOf('维护保养情况');
    const emptyColumnIndex = findColumnIndex(headers, ['空仓', '空仓？']);

    dateGroups.forEach((group) => {
        const groupStartRow = processedRows.length;

        group.forEach((item, groupIndex) => {
            const row = [...item.row];

            // 处理日期列合并：保持原始日期值，但在Excel中会通过合并单元格来显示
            // 不要清空日期值，让Excel的合并单元格功能来处理显示

            // 处理维护保养情况列合并：只在第一行显示
            if (groupIndex > 0 && maintenanceColumnIndex !== -1) {
                row[maintenanceColumnIndex] = '';
            }
            processedRows.push(row);
        });

        // 记录合并信息
        if (group.length > 1) {
            // 日期列合并
            mergeInfo.push({
                startRow: groupStartRow,
                endRow: groupStartRow + group.length - 1,
                startCol: dateColumnIndex,
                endCol: dateColumnIndex,
                type: 'date'
            });

            // 维护保养情况列合并
            if (maintenanceColumnIndex !== -1) {
                mergeInfo.push({
                    startRow: groupStartRow,
                    endRow: groupStartRow + group.length - 1,
                    startCol: maintenanceColumnIndex,
                    endCol: maintenanceColumnIndex,
                    type: 'maintenance'
                });
            }

            // 处理空仓列的条件合并
            if (emptyColumnIndex !== -1) {
                processEmptyColumnMerge(processedRows, groupStartRow, group.length, emptyColumnIndex, mergeInfo);
            }
        }
    });

    return { rows: processedRows, mergeInfo };
}

// 查找列索引的通用函数
function findColumnIndex(headers, keywords) {
    for (let i = 0; i < headers.length; i++) {
        const header = String(headers[i]).toLowerCase();
        if (keywords.some(keyword => header.includes(keyword.toLowerCase()))) {
            return i;
        }
    }
    return -1;
}

// 格式化日期值 - 返回Date对象用于分组，保持Excel中的日期类型
function formatDateValue(dateValue) {
    if (!dateValue) return null;

    console.log('formatDateValue input:', dateValue, 'type:', typeof dateValue);

    // 如果是Excel日期序列号，使用SheetJS的转换函数
    if (typeof dateValue === 'number' && dateValue > 1) {
        try {
            // 使用SheetJS的日期转换函数，它正确处理Excel的日期系统
            const jsDate = XLSX.SSF.parse_date_code(dateValue);
            if (jsDate && jsDate.y && jsDate.m && jsDate.d) {
                // 创建只包含日期部分的Date对象（时间设为00:00:00）
                const date = new Date(jsDate.y, jsDate.m - 1, jsDate.d, 0, 0, 0, 0);
                console.log('Excel serial number converted:', dateValue, '->', date);
                return date;
            }
        } catch (error) {
            console.warn('Excel date conversion failed, trying manual conversion:', error);
        }

        // 备用转换方法：Excel日期序列号转换
        // Excel的日期序列号1对应1900年1月1日，但需要考虑Excel的1900年闰年bug
        try {
            // 更准确的Excel日期转换
            // Excel日期系统：1900年1月1日 = 1，但Excel错误地认为1900年是闰年
            let excelDate = dateValue;

            // 处理Excel的1900年闰年bug：如果日期 >= 60（1900年3月1日），需要减1
            if (excelDate >= 60) {
                excelDate = excelDate - 1;
            }

            // 计算从1900年1月1日开始的天数
            const baseDate = new Date(1900, 0, 1); // 1900年1月1日
            const resultDate = new Date(baseDate.getTime() + (excelDate - 1) * 86400 * 1000);

            // 确保只包含日期部分
            const finalDate = new Date(resultDate.getFullYear(), resultDate.getMonth(), resultDate.getDate(), 0, 0, 0, 0);
            console.log('Manual Excel date conversion:', dateValue, '->', finalDate);
            return finalDate;
        } catch (error) {
            console.warn('Manual date conversion also failed:', error);
        }
    }

    // 如果是字符串，尝试解析为日期
    if (typeof dateValue === 'string') {
        try {
            const date = new Date(dateValue);
            if (!isNaN(date.getTime())) {
                // 确保只包含日期部分
                const finalDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0);
                console.log('String date converted:', dateValue, '->', finalDate);
                return finalDate;
            }
        } catch (error) {
            console.warn('String date parsing failed:', error);
        }
        // 如果无法解析为日期，返回原字符串
        return dateValue;
    }

    // 如果已经是Date对象，确保只包含日期部分
    if (dateValue instanceof Date) {
        const finalDate = new Date(dateValue.getFullYear(), dateValue.getMonth(), dateValue.getDate(), 0, 0, 0, 0);
        console.log('Date object normalized:', dateValue, '->', finalDate);
        return finalDate;
    }

    console.warn('Unable to convert date value:', dateValue);
    return String(dateValue);
}

// 获取日期的字符串表示用于分组
function getDateGroupKey(dateValue) {
    const formattedDate = formatDateValue(dateValue);

    if (formattedDate instanceof Date) {
        // 使用本地时间而不是UTC时间，避免时区偏差
        const year = formattedDate.getFullYear();
        const month = String(formattedDate.getMonth() + 1).padStart(2, '0');
        const day = String(formattedDate.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    return String(formattedDate || '');
}

// 处理空仓列的条件合并
function processEmptyColumnMerge(rows, startRow, groupSize, emptyColumnIndex, mergeInfo) {
    // 检查组内空仓列的数据情况
    const emptyValues = [];
    for (let i = 0; i < groupSize; i++) {
        const value = rows[startRow + i][emptyColumnIndex];
        emptyValues.push(value);
    }

    // 如果所有值都为空或相同，则可以合并
    const nonEmptyValues = emptyValues.filter(v => v && String(v).trim() !== '');
    const uniqueValues = [...new Set(nonEmptyValues)];

    if (uniqueValues.length <= 1) {
        // 只在第一行保留值，其他行清空
        for (let i = 1; i < groupSize; i++) {
            rows[startRow + i][emptyColumnIndex] = '';
        }

        // 记录合并信息
        mergeInfo.push({
            startRow: startRow,
            endRow: startRow + groupSize - 1,
            startCol: emptyColumnIndex,
            endCol: emptyColumnIndex,
            type: 'empty'
        });
    }
}

// 创建格式化的工作簿
function createFormattedWorkbook(data) {
    // 创建新工作簿
    const newWorkbook = XLSX.utils.book_new();

    // 准备数据 - 添加主标题行
    const mainTitle = ['科陆流水线日常运维及故障处理情况'];
    const worksheetData = [
        mainTitle,
        data.headers,
        ...data.rows
    ];

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    // 应用格式化
    applyWorksheetFormatting(worksheet, data);

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(newWorkbook, worksheet, '运维日志');

    return newWorkbook;
}

// 应用工作表格式化
function applyWorksheetFormatting(worksheet, data) {
    const range = XLSX.utils.decode_range(worksheet['!ref']);
    const numCols = data.headers.length;

    // 设置列宽
    const columnWidths = getColumnWidths(data.headers);
    worksheet['!cols'] = columnWidths;

    // 设置行高 - 任务9要求：统一行高25像素
    const rowHeights = [];
    for (let i = 0; i <= range.e.r; i++) {
        rowHeights.push({ hpt: 25 }); // 25像素行高，适合自动换行显示
    }
    worksheet['!rows'] = rowHeights;

    // 合并主标题行
    const titleMerge = {
        s: { r: 0, c: 0 },
        e: { r: 0, c: numCols - 1 }
    };

    if (!worksheet['!merges']) {
        worksheet['!merges'] = [];
    }
    worksheet['!merges'].push(titleMerge);

    // 应用数据合并（基于mergeInfo）
    if (data.mergeInfo && data.mergeInfo.length > 0) {
        data.mergeInfo.forEach(merge => {
            // 调整行号（因为添加了标题行）
            const adjustedMerge = {
                s: { r: merge.startRow + 2, c: merge.startCol }, // +2 因为有主标题和列标题
                e: { r: merge.endRow + 2, c: merge.endCol }
            };
            worksheet['!merges'].push(adjustedMerge);
        });
    }

    // 应用单元格样式（注意：SheetJS免费版对样式支持有限）
    applyCellStyles(worksheet, range, numCols);
}

// 获取列宽设置
// 根据任务9要求：日期:15, 维护保养情况:25, 故障处理情况:50, 空仓？:15, 备注:15
function getColumnWidths(headers) {
    const defaultWidths = {
        '日期': 15,
        '维护保养情况': 25,
        '故障处理情况': 50,
        '空仓？': 15,
        '空仓': 15,  // 兼容不同的列名变体
        '备注': 15
    };

    return headers.map(header => {
        const width = defaultWidths[header] || 20; // 未知列默认20字符宽度
        return { wch: width };
    });
}

// 应用单元格样式
function applyCellStyles(worksheet, range, numCols) {
    // 定义边框样式
    const borderStyle = {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'thin', color: { rgb: '000000' } },
        right: { style: 'thin', color: { rgb: '000000' } }
    };

    // 定义样式模板
    const styles = {
        mainTitle: {
            font: {
                name: '宋体',
                size: 14,
                bold: true,
                color: { rgb: '000000' }
            },
            alignment: {
                horizontal: 'center',
                vertical: 'center'
            }
        },
        columnHeader: {
            font: {
                name: '宋体',
                size: 12,
                bold: true,
                color: { rgb: 'FFFFFF' }
            },
            fill: {
                patternType: 'solid',
                fgColor: { rgb: '4472C4' }
            },
            alignment: {
                horizontal: 'center',
                vertical: 'center'
            },
            border: borderStyle
        },
        dataCell: {
            font: {
                name: '宋体',
                size: 11,
                color: { rgb: '000000' }
            },
            alignment: {
                horizontal: 'center',  // 任务9要求：其他列居中对齐
                vertical: 'center',
                wrapText: true         // 任务9要求：启用自动换行功能
            },
            border: borderStyle
        },
        dataCellLeft: {
            font: {
                name: '宋体',
                size: 11,
                color: { rgb: '000000' }
            },
            alignment: {
                horizontal: 'left',    // 任务9要求：故障处理情况列左对齐
                vertical: 'center',
                wrapText: true         // 任务9要求：启用自动换行功能
            },
            border: borderStyle
        },
        dateCell: {
            font: {
                name: '宋体',
                size: 11,
                color: { rgb: '000000' }
            },
            alignment: {
                horizontal: 'center',
                vertical: 'center',
                wrapText: true
            },
            border: borderStyle,
            numFmt: 'yyyy-mm-dd'  // 设置日期格式，只显示年月日
        }
    };

    try {
        // 获取故障处理情况列和日期列的索引
        const failureColumnIndex = getFailureColumnIndex(worksheet, numCols);
        const dateColumnIndex = getDateColumnIndex(worksheet, numCols);

        for (let R = range.s.r; R <= range.e.r; ++R) {
            for (let C = range.s.c; C <= range.e.c; ++C) {
                const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });

                // 确保单元格存在
                if (!worksheet[cellAddress]) {
                    worksheet[cellAddress] = { t: 's', v: '' };
                }

                // 应用样式
                if (R === 0) {
                    // 主标题行
                    worksheet[cellAddress].s = JSON.parse(JSON.stringify(styles.mainTitle));
                } else if (R === 1) {
                    // 列标题行
                    worksheet[cellAddress].s = JSON.parse(JSON.stringify(styles.columnHeader));
                } else {
                    // 数据行
                    if (C === failureColumnIndex) {
                        // 故障处理情况列 - 左对齐
                        worksheet[cellAddress].s = JSON.parse(JSON.stringify(styles.dataCellLeft));
                    } else if (C === dateColumnIndex) {
                        // 日期列 - 应用日期格式
                        worksheet[cellAddress].s = JSON.parse(JSON.stringify(styles.dateCell));

                        // 确保日期单元格的类型和值正确
                        const cellValue = worksheet[cellAddress].v;
                        console.log('Processing date cell:', cellAddress, 'value:', cellValue, 'type:', typeof cellValue);

                        if (cellValue instanceof Date) {
                            // 如果是Date对象，转换为Excel日期序列号
                            const excelDate = (cellValue.getTime() - new Date(1900, 0, 1).getTime()) / (24 * 60 * 60 * 1000) + 1;
                            // 处理Excel的1900年闰年bug
                            const adjustedExcelDate = excelDate >= 60 ? excelDate + 1 : excelDate;
                            worksheet[cellAddress].v = adjustedExcelDate;
                            worksheet[cellAddress].t = 'n'; // 数字类型，但有日期格式
                            console.log('Date converted to Excel serial:', cellValue, '->', adjustedExcelDate);
                        } else if (typeof cellValue === 'number' && cellValue > 1) {
                            // 如果已经是数字（可能是Excel日期序列号），保持数字类型
                            worksheet[cellAddress].t = 'n';
                            console.log('Keeping numeric date value:', cellValue);
                        } else if (cellValue) {
                            // 尝试解析其他类型的日期值
                            try {
                                const parsedDate = new Date(cellValue);
                                if (!isNaN(parsedDate.getTime())) {
                                    const excelDate = (parsedDate.getTime() - new Date(1900, 0, 1).getTime()) / (24 * 60 * 60 * 1000) + 1;
                                    const adjustedExcelDate = excelDate >= 60 ? excelDate + 1 : excelDate;
                                    worksheet[cellAddress].v = adjustedExcelDate;
                                    worksheet[cellAddress].t = 'n';
                                    console.log('Parsed and converted date:', cellValue, '->', adjustedExcelDate);
                                }
                            } catch (error) {
                                console.warn('Failed to parse date value:', cellValue, error);
                            }
                        }
                    } else {
                        // 其他数据列 - 居中对齐
                        worksheet[cellAddress].s = JSON.parse(JSON.stringify(styles.dataCell));
                    }
                }
            }
        }
    } catch (error) {
        console.warn('样式设置失败，使用默认样式:', error);
        // 如果样式设置失败，至少保证基本的对齐方式
        applyBasicStyles(worksheet, range, numCols);
    }
}

// 备用的基本样式设置函数
function applyBasicStyles(worksheet, range, numCols) {
    try {
        const failureColumnIndex = getFailureColumnIndex(worksheet, numCols);

        for (let R = range.s.r; R <= range.e.r; ++R) {
            for (let C = range.s.c; C <= range.e.c; ++C) {
                const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });

                if (!worksheet[cellAddress]) {
                    worksheet[cellAddress] = { t: 's', v: '' };
                }

                if (!worksheet[cellAddress].s) worksheet[cellAddress].s = {};

                if (R === 0) {
                    // 主标题行
                    worksheet[cellAddress].s.alignment = { horizontal: 'center', vertical: 'center' };
                    worksheet[cellAddress].s.font = { bold: true };
                } else if (R === 1) {
                    // 列标题行
                    worksheet[cellAddress].s.alignment = { horizontal: 'center', vertical: 'center' };
                    worksheet[cellAddress].s.font = { bold: true };
                } else {
                    // 数据行
                    worksheet[cellAddress].s.alignment = {
                        horizontal: C === failureColumnIndex ? 'left' : 'center',
                        vertical: 'center',
                        wrapText: true
                    };
                }
            }
        }
    } catch (error) {
        console.warn('基本样式设置也失败:', error);
    }
}

// 获取故障处理情况列的索引
function getFailureColumnIndex(worksheet, numCols) {
    // 查找故障处理情况列
    for (let C = 0; C < numCols; C++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 1, c: C });
        const cell = worksheet[cellAddress];
        if (cell && cell.v && String(cell.v).includes('故障')) {
            return C;
        }
    }
    return -1;
}

// 获取日期列的索引
function getDateColumnIndex(worksheet, numCols) {
    const dateKeywords = ['日期', 'date', '时间', 'time', '日', '月', '年'];

    // 查找日期列
    for (let C = 0; C < numCols; C++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 1, c: C });
        const cell = worksheet[cellAddress];
        if (cell && cell.v) {
            const header = String(cell.v).toLowerCase();
            if (dateKeywords.some(keyword => header.includes(keyword))) {
                return C;
            }
        }
    }
    return -1;
}

// 下载工作簿
// 任务10：文件自动命名功能 - 格式：科陆流水线运维日志YYYYMMDD.xlsx
function downloadWorkbook(workbook) {
    try {
        // 生成文件名 - 使用当前日期作为时间戳
        const today = new Date();
        const dateStr = today.getFullYear() +
                       String(today.getMonth() + 1).padStart(2, '0') +
                       String(today.getDate()).padStart(2, '0');
        const fileName = `科陆流水线运维日志${dateStr}.xlsx`;

        // 生成文件并下载
        XLSX.writeFile(workbook, fileName);
        console.log(`文件下载成功: ${fileName}`);
    } catch (error) {
        console.error('文件下载失败:', error);
        throw error;
    }
}

// 显示消息
function showMessage(text, type = 'info') {
    messageArea.textContent = text;
    messageArea.className = `message ${type}`;
    messageArea.style.display = 'block';
    
    // 3秒后自动隐藏成功消息
    if (type === 'success') {
        setTimeout(() => {
            messageArea.style.display = 'none';
        }, 3000);
    }
}

// 显示/隐藏进度
function showProgress(show) {
    progressSection.style.display = show ? 'block' : 'none';
    if (!show) {
        progressFill.style.width = '0%';
    }
}

// 更新进度
function updateProgress(percent, text) {
    progressFill.style.width = percent + '%';
    progressText.textContent = text;
}

// ===== ExcelJS 相关函数 =====

// 使用ExcelJS创建格式化的工作簿
async function createFormattedWorkbookWithExcelJS(data) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('运维日志');

    // 添加主标题行
    const titleRow = worksheet.addRow(['科陆流水线日常运维及故障处理情况']);

    // 合并主标题行
    worksheet.mergeCells(1, 1, 1, data.headers.length);

    // 设置主标题样式
    const titleCell = worksheet.getCell(1, 1);
    titleCell.font = {
        name: '宋体',
        size: 14,
        bold: true,
        color: { argb: 'FF000000' }
    };
    titleCell.alignment = {
        horizontal: 'center',
        vertical: 'middle'
    };

    // 添加列标题行
    const headerRow = worksheet.addRow(data.headers);

    // 设置列标题样式
    headerRow.eachCell((cell, colNumber) => {
        cell.font = {
            name: '宋体',
            size: 12,
            bold: true,
            color: { argb: 'FFFFFFFF' }
        };
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FF4472C4' }
        };
        cell.alignment = {
            horizontal: 'center',
            vertical: 'middle'
        };
        cell.border = {
            top: { style: 'thin', color: { argb: 'FF000000' } },
            left: { style: 'thin', color: { argb: 'FF000000' } },
            bottom: { style: 'thin', color: { argb: 'FF000000' } },
            right: { style: 'thin', color: { argb: 'FF000000' } }
        };
    });

    // 添加数据行
    data.rows.forEach(rowData => {
        const dataRow = worksheet.addRow(rowData);

        // 设置数据行样式
        dataRow.eachCell((cell, colNumber) => {
            cell.font = {
                name: '宋体',
                size: 11,
                color: { argb: 'FF000000' }
            };

            // 根据列类型设置对齐方式
            const columnHeader = data.headers[colNumber - 1];
            if (columnHeader && columnHeader.includes('故障处理情况')) {
                cell.alignment = {
                    horizontal: 'left',
                    vertical: 'middle',
                    wrapText: true
                };
            } else {
                cell.alignment = {
                    horizontal: 'center',
                    vertical: 'middle',
                    wrapText: true
                };
            }

            cell.border = {
                top: { style: 'thin', color: { argb: 'FF000000' } },
                left: { style: 'thin', color: { argb: 'FF000000' } },
                bottom: { style: 'thin', color: { argb: 'FF000000' } },
                right: { style: 'thin', color: { argb: 'FF000000' } }
            };

            // 处理日期格式
            if (columnHeader && columnHeader.includes('日期') && cell.value instanceof Date) {
                cell.numFmt = 'yyyy-mm-dd';
            }
        });
    });

    // 设置列宽
    const columnWidths = getColumnWidthsForExcelJS(data.headers);
    columnWidths.forEach((width, index) => {
        worksheet.getColumn(index + 1).width = width;
    });

    // 设置行高
    worksheet.eachRow((row, rowNumber) => {
        row.height = 25; // 25像素行高
    });

    // 应用单元格合并
    if (data.mergeInfo && data.mergeInfo.length > 0) {
        data.mergeInfo.forEach(merge => {
            // 调整行号（因为添加了标题行）
            const startRow = merge.startRow + 3; // +2 因为有主标题和列标题，+1 因为ExcelJS从1开始
            const endRow = merge.endRow + 3;
            const startCol = merge.startCol + 1; // ExcelJS从1开始
            const endCol = merge.endCol + 1;

            try {
                worksheet.mergeCells(startRow, startCol, endRow, endCol);
            } catch (error) {
                console.warn('合并单元格失败:', error, merge);
            }
        });
    }

    return workbook;
}

// 获取ExcelJS的列宽设置
function getColumnWidthsForExcelJS(headers) {
    const defaultWidth = 15;
    const columnWidthMap = {
        '日期': 15,
        '维护保养情况': 25,
        '故障处理情况': 50,
        '空仓？': 15,
        '空仓': 15,
        '备注': 15
    };

    return headers.map(header => {
        for (const [keyword, width] of Object.entries(columnWidthMap)) {
            if (header.includes(keyword)) {
                return width;
            }
        }
        return defaultWidth;
    });
}

// 使用ExcelJS下载工作簿
async function downloadWorkbookWithExcelJS(workbook) {
    try {
        // 生成文件名 - 使用当前日期作为时间戳
        const today = new Date();
        const dateStr = today.getFullYear() +
                       String(today.getMonth() + 1).padStart(2, '0') +
                       String(today.getDate()).padStart(2, '0');
        const fileName = `科陆流水线运维日志${dateStr}.xlsx`;

        // 生成文件buffer
        const buffer = await workbook.xlsx.writeBuffer();

        // 创建Blob并下载
        const blob = new Blob([buffer], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });

        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        console.log(`文件下载成功: ${fileName}`);
    } catch (error) {
        console.error('文件下载失败:', error);
        throw error;
    }
}
