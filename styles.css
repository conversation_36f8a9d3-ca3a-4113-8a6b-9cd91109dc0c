/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* 文件上传区域 */
.upload-section {
    margin-bottom: 30px;
}

.upload-area {
    border: 3px dashed #667eea;
    border-radius: 10px;
    padding: 40px 20px;
    text-align: center;
    background: #f8f9ff;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.upload-area:hover {
    border-color: #5a67d8;
    background: #f0f2ff;
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: #4c51bf;
    background: #e6f3ff;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.upload-area h3 {
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.upload-area p {
    color: #666;
    margin-bottom: 20px;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    pointer-events: none;
}

.upload-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-btn:hover {
    background: #5a67d8;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 文件信息显示 */
.file-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    border-left: 4px solid #667eea;
}

.file-info h4 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 5px 0;
}

.info-item .label {
    font-weight: 600;
    color: #555;
}

.info-item .value {
    color: #333;
}

/* 转换控制区域 */
.control-section {
    text-align: center;
    margin-bottom: 30px;
}

.convert-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.convert-btn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.convert-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    box-shadow: none;
}

/* 进度指示器 */
.progress-section {
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    color: #667eea;
    font-weight: 600;
    text-align: center;
}

/* 消息区域 */
.message-section {
    min-height: 50px;
}

.message {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    display: none;
}

.message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

.message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
    display: block;
}

/* 页脚 */
.footer {
    text-align: center;
    margin-top: 30px;
    color: white;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .upload-area {
        padding: 30px 15px;
    }
    
    .convert-btn {
        padding: 12px 30px;
        font-size: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-info {
    animation: fadeIn 0.5s ease;
}

.progress-section {
    animation: fadeIn 0.5s ease;
}
