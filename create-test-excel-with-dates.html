<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建测试Excel文件（包含日期）</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background-color: #0056b3;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>创建测试Excel文件（包含日期）</h1>
    
    <div class="info">
        <p>此工具将创建一个包含日期数据的测试Excel文件，用于测试日期修复功能。</p>
        <p>生成的文件将包含：</p>
        <ul>
            <li>日期列（包含Excel日期序列号和时间戳）</li>
            <li>故障处理情况列</li>
            <li>空仓？列</li>
            <li>记录人列（将被过滤掉）</li>
        </ul>
    </div>
    
    <button onclick="createTestExcel()">创建测试Excel文件</button>
    
    <div id="result"></div>

    <script>
        function createTestExcel() {
            try {
                // 创建测试数据 - 模拟原始输入文件的格式
                const testData = [
                    // 标题行
                    ['科陆流水线日常运维及故障处理情况'],
                    // 列标题
                    ['日期', '故障处理情况', '空仓？', '记录人'],
                    // 数据行 - 使用不同的日期格式来测试转换
                    [new Date(2024, 0, 15, 14, 30, 0), '设备A故障，已修复', '是', '张三'],
                    [new Date(2024, 0, 15, 16, 45, 0), '设备B维护检查', '否', '李四'],
                    [new Date(2024, 0, 16, 9, 15, 0), '设备C异常报警', '', '王五'],
                    [new Date(2024, 0, 16, 11, 20, 0), '设备D定期保养', '是', '赵六'],
                    [new Date(2024, 0, 17, 13, 10, 0), '设备E故障排查', '否', '钱七'],
                    [new Date(2024, 0, 17, 15, 25, 0), '设备F运行正常', '', '孙八']
                ];
                
                // 创建工作表
                const worksheet = XLSX.utils.aoa_to_sheet(testData);
                
                // 设置主标题行合并
                worksheet['!merges'] = [
                    { s: { r: 0, c: 0 }, e: { r: 0, c: 3 } }
                ];
                
                // 设置列宽
                worksheet['!cols'] = [
                    { wch: 20 }, // 日期列
                    { wch: 40 }, // 故障处理情况列
                    { wch: 10 }, // 空仓？列
                    { wch: 15 }  // 记录人列
                ];
                
                // 设置日期列格式 - 包含时间，用于测试是否能正确转换为只显示日期
                const range = XLSX.utils.decode_range(worksheet['!ref']);
                for (let R = 2; R <= range.e.r; R++) { // 从数据行开始
                    const cellAddress = XLSX.utils.encode_cell({ r: R, c: 0 }); // 日期列
                    if (worksheet[cellAddress] && worksheet[cellAddress].v instanceof Date) {
                        // 保持为Date对象，让Excel自动处理
                        worksheet[cellAddress].t = 'd';
                        worksheet[cellAddress].s = {
                            numFmt: 'yyyy-mm-dd hh:mm:ss' // 原始格式包含时间
                        };
                    }
                }
                
                // 创建工作簿
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
                
                // 下载文件
                const fileName = `测试数据_${new Date().toISOString().slice(0, 10)}.xlsx`;
                XLSX.writeFile(workbook, fileName);
                
                document.getElementById('result').innerHTML = `
                    <div class="info">
                        <h3>测试文件创建成功！</h3>
                        <p><strong>文件名：</strong>${fileName}</p>
                        <p><strong>说明：</strong>此文件包含带有时间戳的日期数据，可以用来测试转换器是否能正确：</p>
                        <ul>
                            <li>将日期时间转换为只显示年月日</li>
                            <li>修复1970/1/1的显示问题</li>
                            <li>正确处理Excel日期格式</li>
                        </ul>
                        <p><strong>使用方法：</strong>将此文件上传到主转换器页面进行测试。</p>
                    </div>
                `;
                
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <div style="background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;">
                        <h3>创建失败</h3>
                        <p>错误信息：${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
