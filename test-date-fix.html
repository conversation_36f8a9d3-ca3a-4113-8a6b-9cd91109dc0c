<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期格式修复测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <h1>日期格式修复测试</h1>
    <button onclick="createTestFile()">创建测试Excel文件</button>
    <button onclick="testDateFormatting()">测试日期格式化函数</button>
    
    <div id="output"></div>

    <script>
        // 复制修复后的日期格式化函数
        function formatDateValue(dateValue) {
            if (!dateValue) return null;

            console.log('formatDateValue input:', dateValue, 'type:', typeof dateValue);

            // 如果是Excel日期序列号，使用SheetJS的转换函数
            if (typeof dateValue === 'number' && dateValue > 1) {
                try {
                    // 使用SheetJS的日期转换函数，它正确处理Excel的日期系统
                    const jsDate = XLSX.SSF.parse_date_code(dateValue);
                    if (jsDate) {
                        const date = new Date(jsDate.y, jsDate.m - 1, jsDate.d);
                        console.log('Excel serial number converted:', dateValue, '->', date);
                        return date;
                    }
                } catch (error) {
                    console.warn('Excel date conversion failed, trying manual conversion:', error);
                }

                // 备用转换方法：Excel日期序列号转换
                const excelEpoch = new Date(1899, 11, 30); // 1899年12月30日
                const date = new Date(excelEpoch.getTime() + (dateValue * 86400 * 1000));
                console.log('Manual Excel date conversion:', dateValue, '->', date);
                return date;
            }

            // 如果是字符串，尝试解析为日期
            if (typeof dateValue === 'string') {
                const date = new Date(dateValue);
                if (!isNaN(date.getTime())) {
                    console.log('String date parsed:', dateValue, '->', date);
                    return date;
                }
                // 如果无法解析为日期，返回原字符串
                console.log('String cannot be parsed as date:', dateValue);
                return dateValue;
            }

            // 如果已经是Date对象，直接返回
            if (dateValue instanceof Date) {
                console.log('Already a Date object:', dateValue);
                return dateValue;
            }

            console.log('Converting to string:', dateValue);
            return String(dateValue);
        }

        function getDateGroupKey(dateValue) {
            const formattedDate = formatDateValue(dateValue);

            if (formattedDate instanceof Date) {
                // 使用本地时间而不是UTC时间，避免时区偏差
                const year = formattedDate.getFullYear();
                const month = String(formattedDate.getMonth() + 1).padStart(2, '0');
                const day = String(formattedDate.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }

            return String(formattedDate || '');
        }

        function createTestFile() {
            // 创建测试数据
            const testData = [
                ['科陆流水线日常运维及故障处理情况'],
                ['日期', '故障处理情况', '空仓？', '记录人'],
                [new Date('2024-01-15'), '设备检修', '是', '张三'],
                [new Date('2024-01-16'), '系统维护', '否', '李四'],
                [new Date('2024-01-17'), '故障排除', '是', '王五']
            ];

            // 创建工作簿
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(testData);
            
            // 设置日期格式
            const range = XLSX.utils.decode_range(ws['!ref']);
            for (let R = 2; R <= range.e.r; R++) {
                const cellAddress = XLSX.utils.encode_cell({ r: R, c: 0 });
                if (ws[cellAddress]) {
                    ws[cellAddress].t = 'd';
                    ws[cellAddress].s = { numFmt: 'yyyy-mm-dd' };
                }
            }

            XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
            XLSX.writeFile(wb, '测试文件_日期格式.xlsx');
            
            document.getElementById('output').innerHTML += '<p>测试文件已创建：测试文件_日期格式.xlsx</p>';
        }

        function testDateFormatting() {
            const output = document.getElementById('output');
            output.innerHTML += '<h3>日期格式化测试结果：</h3>';

            // 测试不同类型的日期值，包括真实的Excel日期序列号
            const testCases = [
                new Date('2024-01-15'),
                '2024-01-16',
                45307, // Excel日期序列号 (2024-01-15)
                45308, // Excel日期序列号 (2024-01-16)
                44927, // Excel日期序列号 (2022-12-12)
                null,
                undefined,
                ''
            ];

            testCases.forEach((testCase, index) => {
                console.log(`\n=== 测试案例 ${index + 1} ===`);
                const formatted = formatDateValue(testCase);
                const groupKey = getDateGroupKey(testCase);

                output.innerHTML += `<p>测试 ${index + 1}: 输入=${testCase}, 格式化结果=${formatted}, 分组键=${groupKey}</p>`;
            });
        }
    </script>
</body>
</html>
