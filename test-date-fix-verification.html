<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期修复验证测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #console {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>日期修复验证测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>此页面用于测试日期处理修复是否有效：</p>
        <ul>
            <li>测试Excel日期序列号转换</li>
            <li>测试日期格式化（只显示年月日）</li>
            <li>测试生成的Excel文件中日期是否正确</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>日期转换测试</h2>
        <button onclick="testDateConversion()">测试日期转换函数</button>
        <div id="dateTestResults"></div>
    </div>

    <div class="test-section">
        <h2>Excel生成测试</h2>
        <button onclick="createTestExcel()">生成测试Excel文件</button>
        <div id="excelTestResults"></div>
    </div>

    <div class="test-section">
        <h2>控制台输出</h2>
        <div id="console"></div>
        <button onclick="clearConsole()">清空控制台</button>
    </div>

    <script>
        // 重定向console.log到页面
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        
        function addToConsole(message, type = 'log') {
            const consoleDiv = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            consoleDiv.appendChild(logEntry);
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        function clearConsole() {
            document.getElementById('console').innerHTML = '';
        }

        // 复制修复后的formatDateValue函数
        function formatDateValue(dateValue) {
            if (!dateValue) return null;

            console.log('formatDateValue input:', dateValue, 'type:', typeof dateValue);

            // 如果是Excel日期序列号，使用SheetJS的转换函数
            if (typeof dateValue === 'number' && dateValue > 1) {
                try {
                    // 使用SheetJS的日期转换函数，它正确处理Excel的日期系统
                    const jsDate = XLSX.SSF.parse_date_code(dateValue);
                    if (jsDate && jsDate.y && jsDate.m && jsDate.d) {
                        // 创建只包含日期部分的Date对象（时间设为00:00:00）
                        const date = new Date(jsDate.y, jsDate.m - 1, jsDate.d, 0, 0, 0, 0);
                        console.log('Excel serial number converted:', dateValue, '->', date);
                        return date;
                    }
                } catch (error) {
                    console.warn('Excel date conversion failed, trying manual conversion:', error);
                }

                // 备用转换方法：Excel日期序列号转换
                try {
                    // 更准确的Excel日期转换
                    let excelDate = dateValue;
                    
                    // 处理Excel的1900年闰年bug：如果日期 >= 60（1900年3月1日），需要减1
                    if (excelDate >= 60) {
                        excelDate = excelDate - 1;
                    }
                    
                    // 计算从1900年1月1日开始的天数
                    const baseDate = new Date(1900, 0, 1); // 1900年1月1日
                    const resultDate = new Date(baseDate.getTime() + (excelDate - 1) * 86400 * 1000);
                    
                    // 确保只包含日期部分
                    const finalDate = new Date(resultDate.getFullYear(), resultDate.getMonth(), resultDate.getDate(), 0, 0, 0, 0);
                    console.log('Manual Excel date conversion:', dateValue, '->', finalDate);
                    return finalDate;
                } catch (error) {
                    console.warn('Manual date conversion also failed:', error);
                }
            }

            // 如果是字符串，尝试解析为日期
            if (typeof dateValue === 'string') {
                try {
                    const date = new Date(dateValue);
                    if (!isNaN(date.getTime())) {
                        // 确保只包含日期部分
                        const finalDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0);
                        console.log('String date converted:', dateValue, '->', finalDate);
                        return finalDate;
                    }
                } catch (error) {
                    console.warn('String date parsing failed:', error);
                }
                // 如果无法解析为日期，返回原字符串
                return dateValue;
            }

            // 如果已经是Date对象，确保只包含日期部分
            if (dateValue instanceof Date) {
                const finalDate = new Date(dateValue.getFullYear(), dateValue.getMonth(), dateValue.getDate(), 0, 0, 0, 0);
                console.log('Date object normalized:', dateValue, '->', finalDate);
                return finalDate;
            }
            
            console.warn('Unable to convert date value:', dateValue);
            return String(dateValue);
        }

        function testDateConversion() {
            const resultsDiv = document.getElementById('dateTestResults');
            resultsDiv.innerHTML = '';
            
            console.log('=== 开始日期转换测试 ===');
            
            // 测试用例
            const testCases = [
                { input: 45292, description: 'Excel日期序列号 (2024-01-15)' },
                { input: 44927, description: 'Excel日期序列号 (2023-01-01)' },
                { input: '2024-01-15', description: '字符串日期' },
                { input: new Date(2024, 0, 15), description: 'Date对象' },
                { input: 0, description: '无效数字' },
                { input: null, description: 'null值' },
                { input: '', description: '空字符串' }
            ];
            
            testCases.forEach((testCase, index) => {
                try {
                    const result = formatDateValue(testCase.input);
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'test-result info';
                    resultDiv.innerHTML = `
                        <strong>测试 ${index + 1}:</strong> ${testCase.description}<br>
                        <strong>输入:</strong> ${JSON.stringify(testCase.input)}<br>
                        <strong>输出:</strong> ${result instanceof Date ? result.toLocaleDateString('zh-CN') : JSON.stringify(result)}<br>
                        <strong>类型:</strong> ${typeof result}
                    `;
                    resultsDiv.appendChild(resultDiv);
                } catch (error) {
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `
                        <strong>测试 ${index + 1} 失败:</strong> ${testCase.description}<br>
                        <strong>错误:</strong> ${error.message}
                    `;
                    resultsDiv.appendChild(resultDiv);
                }
            });
            
            console.log('=== 日期转换测试完成 ===');
        }

        function createTestExcel() {
            const resultsDiv = document.getElementById('excelTestResults');
            resultsDiv.innerHTML = '';
            
            console.log('=== 开始Excel生成测试 ===');
            
            try {
                // 创建测试数据
                const testData = [
                    ['科陆流水线日常运维及故障处理情况'],
                    ['日期', '故障处理情况', '空仓？', '备注', '维护保养情况'],
                    [new Date(2024, 0, 15), '测试故障1', '是', '已解决', '正常维护'],
                    [new Date(2024, 0, 16), '测试故障2', '否', '已解决', '正常维护'],
                    [new Date(2024, 0, 17), '测试故障3', '', '已解决', '正常维护']
                ];
                
                // 创建工作表
                const worksheet = XLSX.utils.aoa_to_sheet(testData);
                
                // 设置日期格式
                const range = XLSX.utils.decode_range(worksheet['!ref']);
                for (let R = 2; R <= range.e.r; R++) { // 从数据行开始
                    const cellAddress = XLSX.utils.encode_cell({ r: R, c: 0 }); // 日期列
                    if (worksheet[cellAddress]) {
                        // 转换Date对象为Excel日期序列号
                        const cellValue = worksheet[cellAddress].v;
                        if (cellValue instanceof Date) {
                            const excelDate = (cellValue.getTime() - new Date(1900, 0, 1).getTime()) / (24 * 60 * 60 * 1000) + 1;
                            const adjustedExcelDate = excelDate >= 60 ? excelDate + 1 : excelDate;
                            worksheet[cellAddress].v = adjustedExcelDate;
                            worksheet[cellAddress].t = 'n';
                            worksheet[cellAddress].s = {
                                numFmt: 'yyyy-mm-dd'
                            };
                            console.log(`设置单元格 ${cellAddress} 日期值:`, adjustedExcelDate);
                        }
                    }
                }
                
                // 创建工作簿
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, worksheet, '测试');
                
                // 下载文件
                const fileName = `日期修复测试_${new Date().toISOString().slice(0, 10)}.xlsx`;
                XLSX.writeFile(workbook, fileName);
                
                const resultDiv = document.createElement('div');
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `
                    <strong>Excel文件生成成功!</strong><br>
                    文件名: ${fileName}<br>
                    请检查下载的文件中日期列是否正确显示为年月日格式
                `;
                resultsDiv.appendChild(resultDiv);
                
                console.log('Excel文件生成完成:', fileName);
                
            } catch (error) {
                const resultDiv = document.createElement('div');
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `
                    <strong>Excel生成失败:</strong><br>
                    ${error.message}
                `;
                resultsDiv.appendChild(resultDiv);
                console.error('Excel生成错误:', error);
            }
            
            console.log('=== Excel生成测试完成 ===');
        }
    </script>
</body>
</html>
