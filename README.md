# Excel转换器 - 科陆流水线运维日志

一个基于Web的Excel文件格式转换工具，专门用于处理科陆流水线运维日志数据。

## 功能特点

- 🌐 **纯Web应用**: 无需安装，直接在浏览器中使用
- 📁 **文件上传**: 支持拖拽上传和点击选择
- 🔄 **自动转换**: 按照预定义规则处理Excel数据
- 📊 **格式化输出**: 生成符合要求的运维日志格式
- 💾 **自动下载**: 转换完成后自动下载结果文件

## 使用方法

1. **打开应用**: 在浏览器中打开 `index.html` 文件
2. **选择文件**: 点击"选择文件"按钮或直接拖拽 `.xlsx` 文件到上传区域
3. **开始转换**: 点击"开始转换"按钮
4. **下载结果**: 转换完成后文件会自动下载

## 转换规则

- 移除"记录人"列
- 保留所有其他原始列数据
- 添加"备注"列（默认值："已解决"）
- 添加"维护保养情况"列（空白）
- 自动生成文件名：`科陆流水线运维日志YYYYMMDD.xlsx`

## 技术栈

- **HTML5**: 页面结构
- **CSS3**: 样式和布局
- **JavaScript (ES6+)**: 核心逻辑
- **SheetJS**: Excel文件处理库

## 浏览器兼容性

支持所有现代浏览器：
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 文件结构

```
├── index.html      # 主页面
├── styles.css      # 样式文件
├── script.js       # 核心逻辑
└── README.md       # 说明文档
```

## 注意事项

- 仅支持 `.xlsx` 格式的Excel文件
- 文件大小限制：10MB
- 所有处理都在浏览器端完成，数据不会上传到服务器
- 建议使用最新版本的浏览器以获得最佳体验

## 开发状态

当前版本实现了基础功能：
- ✅ 项目初始化和基础HTML结构
- ✅ 文件上传界面
- ✅ Excel文件读取功能
- ✅ 基础数据处理逻辑
- ✅ 文件下载功能

## 后续开发计划

- 数据分组和单元格合并
- Excel格式化和样式设置
- 错误处理优化
- 用户体验改进

## 许可证

本项目仅供内部使用。
