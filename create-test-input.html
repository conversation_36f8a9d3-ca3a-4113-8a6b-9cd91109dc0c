<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建测试输入文件</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>创建测试输入文件</h1>
    
    <div class="info">
        <p>此工具用于创建符合输入格式要求的测试Excel文件，用于验证转换器的表头格式处理。</p>
    </div>

    <button onclick="createTestInputFile()">创建测试输入文件</button>
    <p id="status"></p>

    <script>
        async function createTestInputFile() {
            const statusEl = document.getElementById('status');
            statusEl.textContent = '正在创建测试输入文件...';
            
            try {
                const workbook = new ExcelJS.Workbook();
                const worksheet = workbook.addWorksheet('运维日志');

                // 添加主标题行（模拟输入文件的格式）
                const titleRow = worksheet.addRow(['科陆流水线日常运维及故障处理情况']);
                worksheet.mergeCells(1, 1, 1, 5);

                // 添加列标题行（包含记录人列，用于测试过滤功能）
                const headers = ['日期', '维护保养情况', '故障处理情况', '空仓？', '记录人'];
                worksheet.addRow(headers);

                // 添加测试数据
                const testData = [
                    ['2024-01-01', '正常巡检', '无故障', '否', '张三'],
                    ['2024-01-02', '清洁维护', '传感器故障已修复', '否', '李四'],
                    ['2024-01-03', '例行检查', '无故障', '是', '王五'],
                    ['2024-01-04', '深度保养', '电机异响，已更换轴承', '否', '赵六'],
                    ['2024-01-05', '日常检查', '无故障', '否', '钱七']
                ];

                testData.forEach(row => {
                    worksheet.addRow(row);
                });

                // 设置列宽
                worksheet.getColumn(1).width = 15; // 日期
                worksheet.getColumn(2).width = 20; // 维护保养情况
                worksheet.getColumn(3).width = 30; // 故障处理情况
                worksheet.getColumn(4).width = 10; // 空仓？
                worksheet.getColumn(5).width = 10; // 记录人

                // 生成文件名
                const today = new Date();
                const dateStr = today.getFullYear() +
                               String(today.getMonth() + 1).padStart(2, '0') +
                               String(today.getDate()).padStart(2, '0');
                const fileName = `测试输入文件${dateStr}.xlsx`;

                // 生成Excel文件的buffer
                const buffer = await workbook.xlsx.writeBuffer();

                // 创建Blob对象
                const blob = new Blob([buffer], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                });

                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                statusEl.textContent = '测试输入文件创建成功！文件名：' + fileName;
                console.log('测试输入文件创建成功:', fileName);
            } catch (error) {
                console.error('创建测试输入文件失败:', error);
                statusEl.textContent = '创建失败: ' + error.message;
            }
        }
    </script>
</body>
</html>
