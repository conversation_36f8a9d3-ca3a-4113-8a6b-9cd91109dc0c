<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样式测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <h1>Excel样式测试</h1>
    <button onclick="testStyles()">生成测试Excel文件</button>
    
    <script>
        function testStyles() {
            // 创建测试数据
            const testData = [
                ['科陆流水线日常运维及故障处理情况'],
                ['日期', '维护保养情况', '故障处理情况', '空仓？', '备注'],
                ['2024-01-01', '正常维护', '无故障', '否', '已解决'],
                ['2024-01-01', '', '设备异常', '否', '已解决'],
                ['2024-01-02', '清洁保养', '传感器故障', '是', '已解决']
            ];

            // 创建工作簿和工作表
            const workbook = XLSX.utils.book_new();
            const worksheet = XLSX.utils.aoa_to_sheet(testData);

            // 应用样式
            applyTestStyles(worksheet, testData);

            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(workbook, worksheet, '样式测试');

            // 下载文件
            XLSX.writeFile(workbook, '样式测试.xlsx');
        }

        function applyTestStyles(worksheet, data) {
            const range = XLSX.utils.decode_range(worksheet['!ref']);
            const numCols = data[1].length; // 列标题行的列数

            // 定义边框样式
            const borderStyle = {
                top: { style: 'thin', color: { rgb: '000000' } },
                bottom: { style: 'thin', color: { rgb: '000000' } },
                left: { style: 'thin', color: { rgb: '000000' } },
                right: { style: 'thin', color: { rgb: '000000' } }
            };

            // 定义样式模板
            const styles = {
                mainTitle: {
                    font: { 
                        name: '宋体', 
                        size: 14, 
                        bold: true,
                        color: { rgb: '000000' }
                    },
                    alignment: { 
                        horizontal: 'center', 
                        vertical: 'center' 
                    }
                },
                columnHeader: {
                    font: { 
                        name: '宋体', 
                        size: 12, 
                        bold: true,
                        color: { rgb: 'FFFFFF' }
                    },
                    fill: {
                        patternType: 'solid',
                        fgColor: { rgb: '4472C4' }
                    },
                    alignment: { 
                        horizontal: 'center', 
                        vertical: 'center' 
                    },
                    border: borderStyle
                },
                dataCell: {
                    font: { 
                        name: '宋体', 
                        size: 11,
                        color: { rgb: '000000' }
                    },
                    alignment: { 
                        horizontal: 'center', 
                        vertical: 'center',
                        wrapText: true
                    },
                    border: borderStyle
                },
                dataCellLeft: {
                    font: { 
                        name: '宋体', 
                        size: 11,
                        color: { rgb: '000000' }
                    },
                    alignment: { 
                        horizontal: 'left', 
                        vertical: 'center',
                        wrapText: true
                    },
                    border: borderStyle
                }
            };

            // 设置列宽
            worksheet['!cols'] = [
                { wch: 15 }, // 日期
                { wch: 25 }, // 维护保养情况
                { wch: 50 }, // 故障处理情况
                { wch: 15 }, // 空仓？
                { wch: 15 }  // 备注
            ];

            // 设置行高
            worksheet['!rows'] = [
                { hpt: 25 }, // 主标题行
                { hpt: 25 }, // 列标题行
                { hpt: 25 }, // 数据行1
                { hpt: 25 }, // 数据行2
                { hpt: 25 }  // 数据行3
            ];

            // 合并主标题行
            if (!worksheet['!merges']) {
                worksheet['!merges'] = [];
            }
            worksheet['!merges'].push({
                s: { r: 0, c: 0 },
                e: { r: 0, c: numCols - 1 }
            });

            // 应用单元格样式
            try {
                for (let R = range.s.r; R <= range.e.r; ++R) {
                    for (let C = range.s.c; C <= range.e.c; ++C) {
                        const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });

                        // 确保单元格存在
                        if (!worksheet[cellAddress]) {
                            worksheet[cellAddress] = { t: 's', v: '' };
                        }

                        // 应用样式
                        if (R === 0) {
                            // 主标题行
                            worksheet[cellAddress].s = JSON.parse(JSON.stringify(styles.mainTitle));
                        } else if (R === 1) {
                            // 列标题行
                            worksheet[cellAddress].s = JSON.parse(JSON.stringify(styles.columnHeader));
                        } else {
                            // 数据行
                            if (C === 2) { // 故障处理情况列（索引2）
                                worksheet[cellAddress].s = JSON.parse(JSON.stringify(styles.dataCellLeft));
                            } else {
                                worksheet[cellAddress].s = JSON.parse(JSON.stringify(styles.dataCell));
                            }
                        }
                    }
                }
                console.log('样式应用成功');
            } catch (error) {
                console.error('样式应用失败:', error);
            }
        }
    </script>
</body>
</html>
